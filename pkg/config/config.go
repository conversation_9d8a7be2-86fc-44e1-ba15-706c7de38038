package config

import (
	"time"
)

// Config represents the complete gateway configuration
type Config struct {
	Server    ServerConfig    `yaml:"server" mapstructure:"server"`
	Logging   LoggingConfig   `yaml:"logging" mapstructure:"logging"`
	Metrics   MetricsConfig   `yaml:"metrics" mapstructure:"metrics"`
	Tracing   TracingConfig   `yaml:"tracing" mapstructure:"tracing"`
	Auth      AuthConfig      `yaml:"auth" mapstructure:"auth"`
	Security  SecurityConfig  `yaml:"security" mapstructure:"security"`
	Routes    []RouteConfig   `yaml:"routes" mapstructure:"routes"`
	Plugins   PluginConfig    `yaml:"plugins" mapstructure:"plugins"`
	Discovery DiscoveryConfig `yaml:"discovery" mapstructure:"discovery"`
}

// ServerConfig contains HTTP server configuration
type ServerConfig struct {
	Address      string    `yaml:"address" mapstructure:"address"`
	ReadTimeout  int       `yaml:"read_timeout" mapstructure:"read_timeout"`
	WriteTimeout int       `yaml:"write_timeout" mapstructure:"write_timeout"`
	IdleTimeout  int       `yaml:"idle_timeout" mapstructure:"idle_timeout"`
	TLS          TLSConfig `yaml:"tls" mapstructure:"tls"`
}

// TLSConfig contains TLS configuration
type TLSConfig struct {
	Enabled  bool   `yaml:"enabled" mapstructure:"enabled"`
	CertFile string `yaml:"cert_file" mapstructure:"cert_file"`
	KeyFile  string `yaml:"key_file" mapstructure:"key_file"`
	MTLSMode string `yaml:"mtls_mode" mapstructure:"mtls_mode"` // none, request, require
}

// LoggingConfig contains logging configuration
type LoggingConfig struct {
	Level      string `yaml:"level" mapstructure:"level"`
	Format     string `yaml:"format" mapstructure:"format"` // json, text
	Output     string `yaml:"output" mapstructure:"output"` // stdout, file
	File       string `yaml:"file" mapstructure:"file"`
	MaxSize    int    `yaml:"max_size" mapstructure:"max_size"`
	MaxBackups int    `yaml:"max_backups" mapstructure:"max_backups"`
	MaxAge     int    `yaml:"max_age" mapstructure:"max_age"`
}

// MetricsConfig contains metrics configuration
type MetricsConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled"`
	Path    string `yaml:"path" mapstructure:"path"`
	Port    int    `yaml:"port" mapstructure:"port"`
}

// TracingConfig contains distributed tracing configuration
type TracingConfig struct {
	Enabled     bool   `yaml:"enabled" mapstructure:"enabled"`
	ServiceName string `yaml:"service_name" mapstructure:"service_name"`
	Endpoint    string `yaml:"endpoint" mapstructure:"endpoint"`
	SampleRate  float64 `yaml:"sample_rate" mapstructure:"sample_rate"`
}

// AuthConfig contains authentication configuration
type AuthConfig struct {
	JWT      JWTConfig      `yaml:"jwt" mapstructure:"jwt"`
	OIDC     OIDCConfig     `yaml:"oidc" mapstructure:"oidc"`
	APIKey   APIKeyConfig   `yaml:"api_key" mapstructure:"api_key"`
	MTLS     MTLSConfig     `yaml:"mtls" mapstructure:"mtls"`
	Policies PolicyConfig   `yaml:"policies" mapstructure:"policies"`
}

// JWTConfig contains JWT authentication configuration
type JWTConfig struct {
	Enabled    bool   `yaml:"enabled" mapstructure:"enabled"`
	Secret     string `yaml:"secret" mapstructure:"secret"`
	PublicKey  string `yaml:"public_key" mapstructure:"public_key"`
	Algorithm  string `yaml:"algorithm" mapstructure:"algorithm"`
	Expiration int    `yaml:"expiration" mapstructure:"expiration"`
}

// OIDCConfig contains OIDC configuration
type OIDCConfig struct {
	Enabled      bool   `yaml:"enabled" mapstructure:"enabled"`
	Issuer       string `yaml:"issuer" mapstructure:"issuer"`
	ClientID     string `yaml:"client_id" mapstructure:"client_id"`
	ClientSecret string `yaml:"client_secret" mapstructure:"client_secret"`
	RedirectURL  string `yaml:"redirect_url" mapstructure:"redirect_url"`
}

// APIKeyConfig contains API key authentication configuration
type APIKeyConfig struct {
	Enabled    bool   `yaml:"enabled" mapstructure:"enabled"`
	HeaderName string `yaml:"header_name" mapstructure:"header_name"`
	QueryParam string `yaml:"query_param" mapstructure:"query_param"`
}

// MTLSConfig contains mutual TLS configuration
type MTLSConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled"`
	CAFile  string `yaml:"ca_file" mapstructure:"ca_file"`
}

// PolicyConfig contains authorization policy configuration
type PolicyConfig struct {
	Engine   string `yaml:"engine" mapstructure:"engine"` // opa, builtin
	OPAPath  string `yaml:"opa_path" mapstructure:"opa_path"`
	Policies []PolicyRule `yaml:"policies" mapstructure:"policies"`
}

// PolicyRule represents a single authorization rule
type PolicyRule struct {
	Name        string            `yaml:"name" mapstructure:"name"`
	Path        string            `yaml:"path" mapstructure:"path"`
	Method      string            `yaml:"method" mapstructure:"method"`
	Roles       []string          `yaml:"roles" mapstructure:"roles"`
	Permissions []string          `yaml:"permissions" mapstructure:"permissions"`
	Conditions  map[string]string `yaml:"conditions" mapstructure:"conditions"`
}

// SecurityConfig contains security-related configuration
type SecurityConfig struct {
	RateLimit RateLimitConfig `yaml:"rate_limit" mapstructure:"rate_limit"`
	CORS      CORSConfig      `yaml:"cors" mapstructure:"cors"`
	WAF       WAFConfig       `yaml:"waf" mapstructure:"waf"`
	IPFilter  IPFilterConfig  `yaml:"ip_filter" mapstructure:"ip_filter"`
}

// RateLimitConfig contains rate limiting configuration
type RateLimitConfig struct {
	Enabled   bool          `yaml:"enabled" mapstructure:"enabled"`
	Algorithm string        `yaml:"algorithm" mapstructure:"algorithm"` // token_bucket, leaky_bucket
	Rules     []RateLimitRule `yaml:"rules" mapstructure:"rules"`
}

// RateLimitRule represents a rate limiting rule
type RateLimitRule struct {
	Path     string        `yaml:"path" mapstructure:"path"`
	Method   string        `yaml:"method" mapstructure:"method"`
	Rate     int           `yaml:"rate" mapstructure:"rate"`
	Burst    int           `yaml:"burst" mapstructure:"burst"`
	Window   time.Duration `yaml:"window" mapstructure:"window"`
	KeyBy    string        `yaml:"key_by" mapstructure:"key_by"` // ip, user, api_key
}

// CORSConfig contains CORS configuration
type CORSConfig struct {
	Enabled          bool     `yaml:"enabled" mapstructure:"enabled"`
	AllowedOrigins   []string `yaml:"allowed_origins" mapstructure:"allowed_origins"`
	AllowedMethods   []string `yaml:"allowed_methods" mapstructure:"allowed_methods"`
	AllowedHeaders   []string `yaml:"allowed_headers" mapstructure:"allowed_headers"`
	ExposedHeaders   []string `yaml:"exposed_headers" mapstructure:"exposed_headers"`
	AllowCredentials bool     `yaml:"allow_credentials" mapstructure:"allow_credentials"`
	MaxAge           int      `yaml:"max_age" mapstructure:"max_age"`
}

// WAFConfig contains Web Application Firewall configuration
type WAFConfig struct {
	Enabled bool      `yaml:"enabled" mapstructure:"enabled"`
	Rules   []WAFRule `yaml:"rules" mapstructure:"rules"`
}

// WAFRule represents a WAF rule
type WAFRule struct {
	Name        string `yaml:"name" mapstructure:"name"`
	Pattern     string `yaml:"pattern" mapstructure:"pattern"`
	Action      string `yaml:"action" mapstructure:"action"` // block, log, allow
	Description string `yaml:"description" mapstructure:"description"`
}

// IPFilterConfig contains IP filtering configuration
type IPFilterConfig struct {
	Enabled   bool     `yaml:"enabled" mapstructure:"enabled"`
	Whitelist []string `yaml:"whitelist" mapstructure:"whitelist"`
	Blacklist []string `yaml:"blacklist" mapstructure:"blacklist"`
}

// RouteConfig represents a routing configuration
type RouteConfig struct {
	Name         string            `yaml:"name" mapstructure:"name"`
	Path         string            `yaml:"path" mapstructure:"path"`
	Method       string            `yaml:"method" mapstructure:"method"`
	Upstream     UpstreamConfig    `yaml:"upstream" mapstructure:"upstream"`
	Rewrite      RewriteConfig     `yaml:"rewrite" mapstructure:"rewrite"`
	Timeout      time.Duration     `yaml:"timeout" mapstructure:"timeout"`
	Retries      int               `yaml:"retries" mapstructure:"retries"`
	Headers      map[string]string `yaml:"headers" mapstructure:"headers"`
	Plugins      []string          `yaml:"plugins" mapstructure:"plugins"`
}

// UpstreamConfig contains upstream service configuration
type UpstreamConfig struct {
	Type            string              `yaml:"type" mapstructure:"type"` // static, discovery
	LoadBalancer    string              `yaml:"load_balancer" mapstructure:"load_balancer"`
	HealthCheck     HealthCheckConfig   `yaml:"health_check" mapstructure:"health_check"`
	Servers         []ServerTarget      `yaml:"servers" mapstructure:"servers"`
	ServiceName     string              `yaml:"service_name" mapstructure:"service_name"`
	DiscoveryConfig DiscoveryConfig     `yaml:"discovery_config" mapstructure:"discovery_config"`
}

// ServerTarget represents an upstream server
type ServerTarget struct {
	Host   string `yaml:"host" mapstructure:"host"`
	Port   int    `yaml:"port" mapstructure:"port"`
	Weight int    `yaml:"weight" mapstructure:"weight"`
	Backup bool   `yaml:"backup" mapstructure:"backup"`
}

// HealthCheckConfig contains health check configuration
type HealthCheckConfig struct {
	Enabled  bool          `yaml:"enabled" mapstructure:"enabled"`
	Path     string        `yaml:"path" mapstructure:"path"`
	Interval time.Duration `yaml:"interval" mapstructure:"interval"`
	Timeout  time.Duration `yaml:"timeout" mapstructure:"timeout"`
	Retries  int           `yaml:"retries" mapstructure:"retries"`
}

// RewriteConfig contains URL rewrite configuration
type RewriteConfig struct {
	Enabled bool   `yaml:"enabled" mapstructure:"enabled"`
	From    string `yaml:"from" mapstructure:"from"`
	To      string `yaml:"to" mapstructure:"to"`
}

// PluginConfig contains plugin configuration
type PluginConfig struct {
	Directory string                 `yaml:"directory" mapstructure:"directory"`
	Plugins   map[string]interface{} `yaml:"plugins" mapstructure:"plugins"`
}

// DiscoveryConfig contains service discovery configuration
type DiscoveryConfig struct {
	Type   string      `yaml:"type" mapstructure:"type"` // consul, nacos, eureka
	Consul ConsulConfig `yaml:"consul" mapstructure:"consul"`
	Nacos  NacosConfig  `yaml:"nacos" mapstructure:"nacos"`
}

// ConsulConfig contains Consul configuration
type ConsulConfig struct {
	Address    string `yaml:"address" mapstructure:"address"`
	Datacenter string `yaml:"datacenter" mapstructure:"datacenter"`
	Token      string `yaml:"token" mapstructure:"token"`
}

// NacosConfig contains Nacos configuration
type NacosConfig struct {
	ServerConfigs []NacosServerConfig `yaml:"server_configs" mapstructure:"server_configs"`
	ClientConfig  NacosClientConfig   `yaml:"client_config" mapstructure:"client_config"`
}

// NacosServerConfig represents Nacos server configuration
type NacosServerConfig struct {
	IpAddr string `yaml:"ip_addr" mapstructure:"ip_addr"`
	Port   uint64 `yaml:"port" mapstructure:"port"`
}

// NacosClientConfig represents Nacos client configuration
type NacosClientConfig struct {
	NamespaceId         string `yaml:"namespace_id" mapstructure:"namespace_id"`
	TimeoutMs           uint64 `yaml:"timeout_ms" mapstructure:"timeout_ms"`
	NotLoadCacheAtStart bool   `yaml:"not_load_cache_at_start" mapstructure:"not_load_cache_at_start"`
	LogDir              string `yaml:"log_dir" mapstructure:"log_dir"`
	CacheDir            string `yaml:"cache_dir" mapstructure:"cache_dir"`
	LogLevel            string `yaml:"log_level" mapstructure:"log_level"`
}

// Load loads configuration from file

